# -*- coding: utf-8 -*-
"""
Helper para análises finais com IA usando modelo "analysis" rápido.
Foca em validação de conformidade com design system e correções finais.
"""

import re
from typing import Dict, Any, Tuple, List

from src.utils.logging import get_logger

logger = get_logger(__name__)


class AIAnalysisHelper:
    """
    Realiza análises e validações finais usando IA para garantir qualidade.
    """
    
    def __init__(self, flow_client, prompts: Dict[str, Any], ai_config: Dict[str, Any]):
        self.flow_client = flow_client
        self.prompts = prompts
        self.ai_config = ai_config
    
    def final_validation_with_ai(self, html: str, typescript: str, scss: str, context: Dict[str, Any]) -> Tuple[str, str, str]:
        """
        Validação final com IA para garantir conformidade com design system.
        
        Args:
            html: Código HTML
            typescript: Código TypeScript
            scss: Código SCSS
            context: Contexto com dados do design system
            
        Returns:
            <PERSON><PERSON>[html_validado, typescript_validado, scss_validado]
        """
        logger.info("🤖 Iniciando validação final com IA...")
        
        try:
            # Preparar contexto para validação
            validation_context = self._prepare_validation_context(html, typescript, scss, context)
            
            # Analisar problemas antes da validação
            files_analysis = validation_context.get('files_analysis', {})
            cross_file_issues = files_analysis.get('cross_file_issues', [])
            
            if cross_file_issues:
                logger.debug(f"⚠️ Problemas identificados antes da validação:")
                for issue in cross_file_issues:
                    logger.debug(f"   - {issue}")
            else:
                logger.info("✅ Pré-análise finalizada: Nenhum problema crítico identificado")
            
            # Executar validação com IA
            validated_files = self._execute_ai_validation(validation_context)
            
            # Extrair arquivos validados
            validated_html = self._extract_validated_html(validated_files)
            validated_typescript = self._extract_validated_typescript(validated_files)
            validated_scss = self._extract_validated_scss(validated_files)

            # Verificar se houve mudanças
            html_changed = html != validated_html
            ts_changed = typescript != validated_typescript
            scss_changed = scss != validated_scss
            
            if html_changed or ts_changed or scss_changed:
                logger.info("✅ Validação final com IA concluída - arquivos corrigidos")
                if html_changed:
                    logger.debug("   - HTML corrigido")
                if ts_changed:
                    logger.debug("   - TypeScript corrigido")
                if scss_changed:
                    logger.debug("   - SCSS corrigido")
            else:
                logger.info("✅ Validação final com IA concluída - nenhuma correção necessária")

            return validated_html, validated_typescript, validated_scss
            
        except Exception as e:
            logger.error(f"❌ Erro na validação final com IA: {e}")
            logger.info("🔄 Retornando arquivos sem validação IA")
            return html, typescript, scss
    
    def _prepare_validation_context(self, html: str, typescript: str, scss: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Prepara contexto específico para validação com IA."""
        
        logger.debug("🔍 Preparando contexto para validação com IA")
        
        # Extrair informações do design system mapeado
        design_system_info = self._extract_design_system_info(context)
        
        # Analisar arquivos gerados
        files_analysis = self._analyze_generated_files(html, typescript, scss)
        
        # Preparar contexto completo
        validation_context = {
            'component_name': context.get('component_name', ''),
            'component_type': context.get('component_type', 'component'),
            'generated_html': html,
            'generated_typescript': typescript,
            'generated_scss': scss,
            'design_system_info': design_system_info,
            'files_analysis': files_analysis,
            'mapped_components': context.get('mapped_components', []),
            'raw_html': context.get('raw_html', ''),
        }
        
        # Adicionar informações específicas dos problemas identificados
        cross_file_issues = files_analysis.get('cross_file_issues', [])
        if cross_file_issues:
            validation_context['identified_problems'] = cross_file_issues
            logger.debug(f"🔍 Problemas identificados: {len(cross_file_issues)}")
            for issue in cross_file_issues:
                logger.debug(f"   - {issue}")
        
        logger.debug(f"✅ Contexto preparado com {len(validation_context)} chaves")
        return validation_context
    
    def _execute_ai_validation(self, context: Dict[str, Any]) -> str:
        """Executa validação com IA usando prompt específico."""
        
        # Usar prompt de validação final
        system_prompt = self.prompts.get('validation', {}).get('final_validation', {}).get('system_prompt', '')
        user_prompt = self.prompts.get('validation', {}).get('final_validation', {}).get('user_prompt', '')
        
        try:
            # Formatar o user_prompt com os dados do contexto de forma segura
            try:
                # Preparar contexto para formatação (convertendo objetos complexos para string)
                format_context = {}
                for key, value in context.items():
                    if isinstance(value, (dict, list)):
                        format_context[key] = str(value)
                    else:
                        format_context[key] = value
                
                formatted_user_prompt = user_prompt.format(**format_context)
            except KeyError as e:
                logger.warning(f"⚠️ Chave não encontrada no contexto: {e}")
                # Lista chaves disponíveis
                logger.warning(f"   Chaves disponíveis: {list(context.keys())}")
                # Usar prompt sem formatação se houver erro
                formatted_user_prompt = user_prompt
            except ValueError as e:
                logger.warning(f"⚠️ Erro na formatação do prompt: {e}")
                # Usar prompt sem formatação se houver erro
                formatted_user_prompt = user_prompt
            except Exception as e:
                logger.warning(f"⚠️ Erro inesperado na formatação do prompt: {e}")
                # Usar prompt sem formatação se houver erro
                formatted_user_prompt = user_prompt
            
            logger.debug(f"🔍 Executando validação com IA")
            logger.debug(f"   System prompt: {len(system_prompt)} caracteres")
            logger.debug(f"   User prompt: {len(formatted_user_prompt)} caracteres")
            
            model = self.ai_config['model']['analysis']
            temperature = self.ai_config.get('temperature')
            max_tokens = self.ai_config.get('max_tokens')
            
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=formatted_user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            logger.debug(f"✅ Resposta da IA recebida: {len(response)} caracteres")
            return response
            
        except Exception as e:
            logger.error(f"❌ Erro na chamada da IA: {e}")
            raise
    
    def _extract_design_system_info(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai informações relevantes do design system para validação."""
        
        design_system_info = {
            'mapped_components': [],
            'required_classes': [],
            'required_structure': {},
            'special_rules': []
        }
        
        # Processar componentes mapeados
        for mapped_comp in context.get('mapped_components', []):
            if 'design_system' in mapped_comp:
                ds_comp = mapped_comp['design_system']
                
                component_info = {
                    'name': ds_comp.get('name', ''),
                    'template': ds_comp.get('template', ''),
                    'category': ds_comp.get('category', ''),
                    'confidence': mapped_comp.get('confidence', 0)
                }
                
                design_system_info['mapped_components'].append(component_info)
                
                # Extrair classes CSS do template
                if 'template' in ds_comp:
                    template = ds_comp['template']
                    classes = re.findall(r'class="([^"]*)"', template)
                    for class_attr in classes:
                        design_system_info['required_classes'].extend(class_attr.split())
        
        # Remover duplicatas
        design_system_info['required_classes'] = list(set(design_system_info['required_classes']))
        
        return design_system_info
    
    def _analyze_generated_files(self, html: str, typescript: str, scss: str) -> Dict[str, Any]:
        """Analisa arquivos gerados para identificar possíveis problemas."""
        
        analysis = {
            'html_analysis': self._analyze_html_structure(html),
            'typescript_analysis': self._analyze_typescript_structure(typescript),
            'scss_analysis': self._analyze_scss_structure(scss),
            'cross_file_issues': self._find_cross_file_issues(html, typescript, scss)
        }
        
        return analysis
    
    def _analyze_html_structure(self, html: str) -> Dict[str, Any]:
        """Analisa estrutura do HTML."""
        return {
            'classes_used': re.findall(r'class="([^"]*)"', html),
            'ids_used': re.findall(r'id="([^"]*)"', html),
            'methods_called': re.findall(r'\(click\)="(\w+)\(\)"', html),
            'has_design_system_classes': bool(re.search(r'brad-\w+', html)),
            'structure_elements': {
                'forms': html.count('<form'),
                'buttons': html.count('<button') + html.count('brad-button'),
                'inputs': html.count('<input') + html.count('brad-text-field'),
                'tables': html.count('brad-table')
            }
        }
    
    def _analyze_typescript_structure(self, typescript: str) -> Dict[str, Any]:
        """Analisa estrutura do TypeScript."""
        return {
            'methods_defined': re.findall(r'(\w+)\(\)\s*{', typescript),
            'properties_defined': re.findall(r'(\w+):\s*\w+', typescript),
            'has_imports': 'import' in typescript,
            'has_component_decorator': '@Component' in typescript,
            'imports_used': re.findall(r'import.*from [\'"]([^\'"]*)[\'"]', typescript)
        }
    
    def _analyze_scss_structure(self, scss: str) -> Dict[str, Any]:
        """Analisa estrutura do SCSS."""
        return {
            'selectors_used': re.findall(r'\.([a-zA-Z][\w-]*)\s*{', scss),
            'has_design_system_classes': bool(re.search(r'\.brad-\w+', scss)),
            'custom_styles_count': len(re.findall(r'{[^}]+}', scss))
        }
    
    def _find_cross_file_issues(self, html: str, typescript: str, scss: str) -> List[str]:
        """Identifica problemas entre arquivos."""
        issues = []
        
        # Métodos chamados no HTML mas não definidos no TS
        html_methods = set(re.findall(r'\(click\)="(\w+)\(\)"', html))
        ts_methods = set(re.findall(r'(\w+)\(\)\s*{', typescript))
        missing_methods = html_methods - ts_methods
        
        if missing_methods:
            issues.append(f"Métodos faltando no TypeScript: {', '.join(missing_methods)}")
        
        # Classes usadas no HTML mas não estilizadas no SCSS
        html_classes = set()
        for class_attr in re.findall(r'class="([^"]*)"', html):
            html_classes.update(class_attr.split())
        
        scss_classes = set(re.findall(r'\.([a-zA-Z][\w-]*)', scss))
        
        # Filtrar classes do design system (não precisam estar no SCSS)
        custom_classes = {cls for cls in html_classes if not cls.startswith('brad-')}
        missing_styles = custom_classes - scss_classes
        
        if missing_styles:
            issues.append(f"Classes sem estilos no SCSS: {', '.join(missing_styles)}")
        
        # PROBLEMAS ESPECÍFICOS IDENTIFICADOS:
        
        # 1. Dados não conectados - verificar se componentes filhos recebem dados
        child_components = re.findall(r'<app-(\w+)', html)
        if child_components:
            for component in child_components:
                # Verificar se o componente filho tem @Input() no TypeScript
                if not re.search(rf'@Input\(\)\s+\w+', typescript):
                    issues.append(f"Componente filho '{component}' não tem @Input() para receber dados do pai")
        
        # 2. Eventos não conectados - verificar se eventos dos filhos são capturados
        child_events = re.findall(r'<app-\w+[^>]*>', html)
        for event_match in child_events:
            # Verificar se há captura de eventos (eventName)="handler()"
            if not re.search(r'\([^)]+\)="[^"]*"', event_match):
                issues.append(f"Eventos do componente filho não estão sendo capturados: {event_match}")
        
        # 3. SCSS com imports inexistentes
        if re.search(r'@import\s+["\']design-system["\']', scss):
            issues.append("SCSS tentando importar 'design-system' que pode não existir")
        
        # 4. HTML usando estilos inline
        inline_styles = re.findall(r'style="[^"]*"', html)
        if inline_styles:
            issues.append(f"HTML usando estilos inline: {len(inline_styles)} ocorrências encontradas")
        
        # 5. Verificar se há @Output() com prefixo "on"
        on_outputs = re.findall(r'@Output\(\)\s+on\w+', typescript)
        if on_outputs:
            issues.append(f"@Output() com prefixo 'on' encontrado: {', '.join(on_outputs)}")
        
        return issues
    
    def _extract_validated_html(self, ai_response: str) -> str:
        """Extrai HTML validado da resposta da IA."""
        logger.debug("🔍 Extraindo HTML validado da resposta da IA")
        
        # Procurar por blocos HTML na resposta
        html_match = re.search(r'```html\n(.*?)\n```', ai_response, re.DOTALL)
        if html_match:
            validated_html = html_match.group(1).strip()
            logger.debug(f"✅ HTML extraído: {len(validated_html)} caracteres")
            return validated_html
        
        # Fallback: procurar por HTML sem marcadores
        html_match = re.search(r'<[^>]+>.*</[^>]+>', ai_response, re.DOTALL)
        if html_match:
            validated_html = html_match.group(0).strip()
            logger.debug(f"✅ HTML extraído (fallback): {len(validated_html)} caracteres")
            return validated_html
        
        # Se não encontrar, retornar resposta original (pode ser que a IA não tenha alterado)
        logger.warning("⚠️ HTML não encontrado na resposta da IA, retornando original")
        return ai_response
    
    def _extract_validated_typescript(self, ai_response: str) -> str:
        """Extrai TypeScript validado da resposta da IA."""
        logger.debug("🔍 Extraindo TypeScript validado da resposta da IA")
        
        # Procurar por blocos TypeScript na resposta
        ts_match = re.search(r'```typescript\n(.*?)\n```', ai_response, re.DOTALL)
        if ts_match:
            validated_ts = ts_match.group(1).strip()
            logger.debug(f"✅ TypeScript extraído: {len(validated_ts)} caracteres")
            return validated_ts
        
        # Fallback: procurar por padrão de classe TypeScript
        ts_match = re.search(r'import.*?export class.*?}', ai_response, re.DOTALL)
        if ts_match:
            validated_ts = ts_match.group(0).strip()
            logger.debug(f"✅ TypeScript extraído (fallback): {len(validated_ts)} caracteres")
            return validated_ts
        
        logger.warning("⚠️ TypeScript não encontrado na resposta da IA, retornando original")
        return ai_response
    
    def _extract_validated_scss(self, ai_response: str) -> str:
        """Extrai SCSS validado da resposta da IA."""
        logger.debug("🔍 Extraindo SCSS validado da resposta da IA")
        
        # Procurar por blocos SCSS na resposta
        scss_match = re.search(r'```scss\n(.*?)\n```', ai_response, re.DOTALL)
        if scss_match:
            validated_scss = scss_match.group(1).strip()
            logger.debug(f"✅ SCSS extraído: {len(validated_scss)} caracteres")
            return validated_scss
        
        # Fallback: procurar por CSS/SCSS
        scss_match = re.search(r'[.#][a-zA-Z].*?{.*?}', ai_response, re.DOTALL)
        if scss_match:
            validated_scss = scss_match.group(0).strip()
            logger.debug(f"✅ SCSS extraído (fallback): {len(validated_scss)} caracteres")
            return validated_scss
        
        logger.warning("⚠️ SCSS não encontrado na resposta da IA, retornando original")
        return ai_response

    def validate_wrapper_with_ai_raw(self, integrated_response: str, context: Dict[str, Any]) -> str:
        """
        Validação específica para componentes wrapper usando resposta integrada completa.

        Args:
            integrated_response: Resposta completa da IA com HTML, TypeScript e SCSS
            context: Contexto com dados do wrapper

        Returns:
            Resposta validada e corrigida pela IA
        """
        logger.info("🤖 Iniciando validação específica de wrapper com resposta integrada...")

        try:
            # Preparar contexto para validação de wrapper com resposta completa
            validation_context = self._prepare_wrapper_validation_context_raw(integrated_response, context)

            # Executar validação com IA
            validated_response = self._execute_wrapper_ai_validation(validation_context)

            # Verificar se houve mudanças
            response_changed = integrated_response != validated_response

            if response_changed:
                logger.info("✅ Validação de wrapper com IA concluída - resposta corrigida")
            else:
                logger.info("✅ Validação de wrapper com IA concluída - nenhuma correção necessária")

            return validated_response

        except Exception as e:
            logger.error(f"❌ Erro na validação de wrapper com IA: {e}")
            logger.info("🔄 Retornando resposta sem validação IA")
            return integrated_response

    def _prepare_wrapper_validation_context_raw(self, integrated_response: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Prepara contexto específico para validação de wrapper com resposta integrada."""

        logger.debug("🔍 Preparando contexto para validação de wrapper com resposta integrada")

        # Extrair arquivos da resposta integrada para o contexto
        html = self._extract_html_from_response(integrated_response)
        typescript = self._extract_typescript_from_response(integrated_response)
        scss = self._extract_scss_from_response(integrated_response)

        # Preparar contexto completo para wrapper
        validation_context = {
            'wrapper_name': context.get('wrapper_name', ''),
            'child_components': context.get('child_components', []),
            'generated_html': html,
            'generated_typescript': typescript,
            'generated_scss': scss,
            'child_components_info': context.get('child_components_info', []),
            'figma_data': context.get('figma_data', {}),
        }

        logger.debug(f"✅ Contexto de wrapper preparado com {len(validation_context)} chaves")
        return validation_context

    def _extract_html_from_response(self, response: str) -> str:
        """Extrai HTML da resposta integrada."""
        import re
        html_match = re.search(r'```html\n(.*?)\n```', response, re.DOTALL)
        if html_match:
            return html_match.group(1).strip()

        # Fallback: procurar HTML sem marcadores
        html_match = re.search(r'<[^>]+>.*</[^>]+>', response, re.DOTALL)
        if html_match:
            return html_match.group(0).strip()

        logger.warning("⚠️ HTML não encontrado na resposta")
        return ""

    def _extract_typescript_from_response(self, response: str) -> str:
        """Extrai TypeScript da resposta integrada."""
        import re
        ts_match = re.search(r'```typescript\n(.*?)\n```', response, re.DOTALL)
        if ts_match:
            return ts_match.group(1).strip()

        # Fallback: procurar padrão de classe TypeScript
        ts_match = re.search(r'import.*?export class.*?}', response, re.DOTALL)
        if ts_match:
            return ts_match.group(0).strip()

        logger.warning("⚠️ TypeScript não encontrado na resposta")
        return ""

    def _extract_scss_from_response(self, response: str) -> str:
        """Extrai SCSS da resposta integrada."""
        import re
        scss_match = re.search(r'```scss\n(.*?)\n```', response, re.DOTALL)
        if scss_match:
            return scss_match.group(1).strip()

        # Fallback: procurar CSS/SCSS
        scss_match = re.search(r'/\*.*?\*/|[.#][a-zA-Z].*?{.*?}', response, re.DOTALL)
        if scss_match:
            return scss_match.group(0).strip()

        logger.warning("⚠️ SCSS não encontrado na resposta")
        return ""

    # Versões anteriores - excluir todos
    def validate_wrapper_with_ai(self, html: str, typescript: str, scss: str, context: Dict[str, Any]) -> Tuple[str, str, str]:
        """
        Validação específica para componentes wrapper com IA.

        Args:
            html: Código HTML do wrapper
            typescript: Código TypeScript do wrapper
            scss: Código SCSS do wrapper
            context: Contexto com dados do wrapper

        Returns:
            Tuple[html_validado, typescript_validado, scss_validado]
        """
        logger.info("🤖 Iniciando validação específica de wrapper com IA...")

        try:
            # Preparar contexto para validação de wrapper
            validation_context = self._prepare_wrapper_validation_context(html, typescript, scss, context)

            # Executar validação com IA
            validated_files = self._execute_wrapper_ai_validation(validation_context)

            # Extrair arquivos validados
            validated_html = self._extract_validated_html(validated_files)
            validated_typescript = self._extract_validated_typescript(validated_files)
            validated_scss = self._extract_validated_scss(validated_files)

            # Verificar se houve mudanças
            html_changed = html != validated_html
            ts_changed = typescript != validated_typescript
            scss_changed = scss != validated_scss

            if html_changed or ts_changed or scss_changed:
                logger.info("✅ Validação de wrapper com IA concluída - arquivos corrigidos")
                if html_changed:
                    logger.debug("   - HTML do wrapper corrigido")
                if ts_changed:
                    logger.debug("   - TypeScript do wrapper corrigido")
                if scss_changed:
                    logger.debug("   - SCSS do wrapper corrigido")
            else:
                logger.info("✅ Validação de wrapper com IA concluída - nenhuma correção necessária")

            return validated_html, validated_typescript, validated_scss

        except Exception as e:
            logger.error(f"❌ Erro na validação de wrapper com IA: {e}")
            logger.info("🔄 Retornando arquivos sem validação IA")
            return html, typescript, scss

    def _prepare_wrapper_validation_context(self, html: str, typescript: str, scss: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Prepara contexto específico para validação de wrapper com IA."""

        logger.debug("🔍 Preparando contexto para validação de wrapper com IA")

        # Preparar contexto completo para wrapper
        validation_context = {
            'wrapper_name': context.get('wrapper_name', ''),
            'child_components': context.get('child_components', []),
            'generated_html': html,
            'generated_typescript': typescript,
            'generated_scss': scss,
            'child_components_info': context.get('child_components_info', []),
            'figma_data': context.get('figma_data', {}),
        }

        logger.debug(f"✅ Contexto de wrapper preparado com {len(validation_context)} chaves")
        return validation_context

    def _execute_wrapper_ai_validation(self, context: Dict[str, Any]) -> str:
        """Executa validação de wrapper com IA usando prompt específico."""

        # Usar prompt de validação de wrapper
        system_prompt = self.prompts.get('validation', {}).get('wrapper_validation', {}).get('system_prompt', '')
        user_prompt = self.prompts.get('validation', {}).get('wrapper_validation', {}).get('user_prompt', '')

        try:
            # Formatar o user_prompt com os dados do contexto de forma segura
            try:
                # Preparar contexto para formatação (convertendo objetos complexos para string)
                format_context = {}
                for key, value in context.items():
                    if isinstance(value, (dict, list)):
                        format_context[key] = str(value)
                    else:
                        format_context[key] = value

                formatted_user_prompt = user_prompt.format(**format_context)
            except KeyError as e:
                logger.warning(f"⚠️ Chave não encontrada no contexto de wrapper: {e}")
                logger.warning(f"   Chaves disponíveis: {list(context.keys())}")
                formatted_user_prompt = user_prompt
            except ValueError as e:
                logger.warning(f"⚠️ Erro na formatação do prompt de wrapper: {e}")
                formatted_user_prompt = user_prompt
            except Exception as e:
                logger.warning(f"⚠️ Erro inesperado na formatação do prompt de wrapper: {e}")
                formatted_user_prompt = user_prompt

            logger.debug(f"🔍 Executando validação de wrapper com IA")
            logger.debug(f"   System prompt: {len(system_prompt)} caracteres")
            logger.debug(f"   User prompt: {len(formatted_user_prompt)} caracteres")

            model = self.ai_config['model']['analysis']
            temperature = self.ai_config.get('temperature')
            max_tokens = self.ai_config.get('max_tokens')

            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=formatted_user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )

            logger.debug(f"✅ Resposta da IA para wrapper recebida: {len(response)} caracteres")
            return response

        except Exception as e:
            logger.error(f"❌ Erro na chamada da IA para wrapper: {e}")
            raise

    def _get_fallback_validation_prompt(self) -> str:
        """Retorna prompt de fallback para validação."""
        return """Você é um especialista em Angular e Design Systems. 
        
Analise os arquivos HTML, TypeScript e SCSS gerados e:

1. Verifique se seguem as especificações do Design System Liquid Bradesco
2. Corrija erros de compilação TypeScript
3. Garanta consistência entre HTML, TypeScript e SCSS
4. Use apenas classes do design system (brad-*) quando possível
5. Corrija estruturas HTML para seguir templates do design system

Retorne os arquivos corrigidos mantendo a funcionalidade original."""
